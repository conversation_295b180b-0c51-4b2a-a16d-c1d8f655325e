plugins {
    java
    id("org.springframework.boot") version "3.5.4"
    id("io.spring.dependency-management") version "1.1.7"
}

group = "com.cdgtaxi"
version = "0.0.1-SNAPSHOT"

java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(17)
    }
}

configurations {
    compileOnly {
        extendsFrom(configurations.annotationProcessor.get())
    }
}

repositories {
    mavenCentral()
}

dependencies {
    // Add all JARs from the lib directory
    implementation(fileTree("libs"))

    // Spring Boot Starter
    implementation("org.springframework.boot:spring-boot-starter")

    // schedule
    implementation("org.springframework.boot:spring-boot-starter-web")

    implementation("com.opencsv:opencsv:5.9")
    implementation("org.springframework.boot:spring-boot-starter-actuator")

    // Lombok dependency
    compileOnly("org.projectlombok:lombok:1.18.30")
    annotationProcessor("org.projectlombok:lombok:1.18.30")

    // SLF4J + Logback log
    implementation("org.slf4j:slf4j-api:2.0.7")

    implementation("com.baomidou:mybatis-plus-boot-starter:3.5.6") {
        exclude("org.mybatis:mybatis-spring")
    }
    implementation("com.baomidou:dynamic-datasource-spring-boot-starter:3.6.1")
    implementation("com.baomidou:mybatis-plus-generator:3.5.6") // code generate
    implementation("org.freemarker:freemarker:2.3.32")           // code generate
    implementation("org.mybatis:mybatis-spring:3.0.3") // support Spring 6 / Spring Boot 3.x

    // test
    testCompileOnly("org.projectlombok:lombok:1.18.30")
    testAnnotationProcessor("org.projectlombok:lombok:1.18.30")
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    
    // 添加Oracle驱动依赖 (使用Maven中央仓库可用的版本)
    implementation("com.oracle.database.jdbc:ojdbc11:21.13.0.0")
    
    // 添加H2数据库依赖 (用于测试)
    implementation("com.h2database:h2:2.2.224")
    
    // 添加Jackson依赖 (用于JSON处理)
    implementation("com.fasterxml.jackson.core:jackson-databind:2.15.2")

    //actuator
    implementation("org.springframework.boot:spring-boot-starter-actuator")

}

tasks.withType<Test> {
    useJUnitPlatform()
}