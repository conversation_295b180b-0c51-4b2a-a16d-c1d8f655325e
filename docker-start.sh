#!/bin/bash

echo "Starting the application with Docker Compose..."

# Build and start the containers
docker-compose up --build -d

echo "Application is starting..."
echo "Frontend will be available at: http://localhost:39010"
echo "Backend API will be available at: http://localhost:39011"
echo ""
echo "To view logs, run:"
echo "  docker-compose logs -f"
echo ""
echo "To stop the application, run:"
echo "  docker-compose down"
