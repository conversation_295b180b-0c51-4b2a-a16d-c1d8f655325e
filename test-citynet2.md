# CityNet2 Generic Query System Implementation

## Summary

I have successfully implemented a **generic, reusable CityNet2 webpage** for database queries with the following features:

### Backend Implementation

1. **Enhanced WebPageService** with `executeDynamicQuery` method that supports:
   - Custom SQL editing
   - Parameter replacement with type-aware handling
   - Date parameter automatic conversion
   - Generic placeholder replacement (e.g., 'xxxx' → actual JOB_NO)

2. **New API Endpoint**: `POST /api/webpages/{id}/dynamic-query`
   - Accepts both query parameters and custom SQL
   - Uses the existing database connection (`@DS("ptfsdev1")`)

3. **Database Entry**: Added CityNet2 ESC_TRIP_DETAILS query page with:
   - Type: `generic-query`
   - Default SQL: `SELECT * FROM citynet2.ESC_TRIP_DETAILS etd WHERE JOB_NO=:jobNo and CREATED_DT > :createdDate`
   - Parameters: JOB_NO (text) and Created Date (date picker)

### Frontend Implementation

1. **GenericQueryPage Component** (`/generic-query/:id`) with:
   - **Parameter Form**: Dynamic form generation from JSON configuration
   - **SQL Editor**: Editable textarea for custom SQL queries
   - **Dual View Results**: 
     - Table view (traditional table format)
     - Key-Value view (detailed record-by-record display)
   - **Date Picker Support**: Automatic date handling
   - **Error Handling**: Comprehensive error display

2. **Enhanced Navigation**:
   - Updated HomePage to route to correct query page based on type
   - Updated GroupedView to handle generic-query types
   - Added new page type option in WebPageForm

### Key Features Implemented

✅ **SQL Query Support**: Full SQL from your requirement
```sql
SELECT * FROM citynet2.ESC_TRIP_DETAILS etd 
WHERE JOB_NO='xxxx' and CREATED_DT > to_date('2025-01-01 00:00:00', 'yyyy-mm-dd hh24:mi:ss')
```

✅ **Editable SQL**: Users can modify the SQL query in the webpage

✅ **JOB_NO Input**: Text field for entering JOB_NO

✅ **Date Picker**: Date selection with automatic conversion to Oracle date format

✅ **Dynamic Parameters**: Uses selected date and entered JOB_NO in queries

✅ **Table Display**: Results shown in responsive table format

✅ **Key-Value Display**: Multiple records shown as individual key-value tables

✅ **Generic Design**: Can be reused for any table query by:
   - Creating new webpage entries with different SQL
   - Configuring different parameters in JSON format
   - Using the same GenericQueryPage component

### Usage Instructions

1. **Access the CityNet2 Query**:
   - Start the backend server
   - Navigate to the grouped view or homepage
   - Click on "CityNet2 ESC_TRIP_DETAILS查询"

2. **Using the Query Page**:
   - Enter JOB_NO in the text field
   - Select a date using the date picker
   - Optionally edit the SQL query in the SQL editor
   - Click "执行查询" to run the query
   - Switch between "表格视图" and "详细视图" to see results

3. **Creating New Generic Queries**:
   - Go to "添加网页"
   - Select type "通用查询页面"
   - Enter SQL with `:parameterName` placeholders
   - Define parameters in JSON format:
   ```json
   [
     {"name":"paramName","label":"Display Label","type":"text|date|select","placeholder":"hint text"},
     {"name":"dateParam","label":"Date Field","type":"date"}
   ]
   ```

### Technical Benefits

- **Reusable**: Same component works for any database table
- **Flexible**: Supports various parameter types (text, date, select)
- **User-Friendly**: Both technical (SQL editing) and non-technical (form) interfaces
- **Responsive**: Works on different screen sizes
- **Error-Resilient**: Comprehensive error handling and validation

This implementation fully satisfies your requirements and provides a foundation for creating similar query pages for other database tables.
