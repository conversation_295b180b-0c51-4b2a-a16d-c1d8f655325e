# Multi-stage build for Spring Boot application
FROM gradle:8.14.3-jdk17 AS build

# Set working directory
WORKDIR /app

# Copy gradle files
COPY build.gradle.kts settings.gradle.kts ./
COPY gradle ./gradle
COPY gradlew gradlew.bat ./

# Copy source code
COPY src ./src

# Build the application
RUN chmod +x gradlew
RUN ./gradlew clean build -x test

# Runtime stage
FROM openjdk:17-jdk-slim

# Set working directory
WORKDIR /app

# Create data directory for H2 database
RUN mkdir -p /app/data

# Copy the built JAR file
COPY --from=build /app/build/libs/*.jar app.jar

# Expose the port
EXPOSE 39011

# Set environment variables
ENV SPRING_PROFILES_ACTIVE=default
ENV JAVA_OPTS="-Xmx512m -Xms256m"

# Run the application
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
