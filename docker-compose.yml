version: '3.8'

#name
name: backendwork

services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: backendwork-api
    ports:
      - "39011:39011"
    environment:
      - SPRING_PROFILES_ACTIVE=default
      - JAVA_OPTS=-Xmx512m -Xms256m
    volumes:
      - ./data:/app/data
    networks:
      - backendwork-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:39011/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    restart: unless-stopped

  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: backendwork-frontend
    ports:
      - "39010:80"
    depends_on:
      - backend
    networks:
      - backendwork-network
    restart: unless-stopped

networks:
  backendwork-network:
    driver: bridge

volumes:
  db-data:
    driver: local
