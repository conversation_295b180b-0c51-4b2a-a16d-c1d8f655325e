# Docker Deployment Guide

This guide explains how to deploy the frontend and backend applications using Docker and Docker Compose.

## Prerequisites

- Docker (version 20.10 or later)
- Docker Compose (version 2.0 or later)

## Quick Start

### 1. Start the Application
```bash
./docker-start.sh
```
Or manually:
```bash
docker-compose up --build -d
```

### 2. Access the Application
- **Frontend**: http://localhost:39010
- **Backend API**: http://localhost:39011
- **H2 Console**: http://localhost:39011/h2-console

### 3. View Logs
```bash
./docker-logs.sh
```
Or manually:
```bash
docker-compose logs -f
```

### 4. Stop the Application
```bash
./docker-stop.sh
```
Or manually:
```bash
docker-compose down
```

## Architecture

The Docker setup consists of:

1. **Backend Service** (`backendwork-api`)
   - Spring Boot application running on port 39011
   - Built using multi-stage Docker build with Gradle
   - Uses H2 database with persistent volume

2. **Frontend Service** (`backendwork-frontend`)
   - React application served by Nginx on port 39010
   - Built using multi-stage Docker build with Node.js
   - Nginx proxies API requests to backend

## Configuration

### Environment Variables

You can customize the deployment by setting environment variables in `docker-compose.yml`:

- `SPRING_PROFILES_ACTIVE`: Spring profile (default: `default`)
- `JAVA_OPTS`: JVM options (default: `-Xmx512m -Xms256m`)

### Ports

- Frontend: 39010 (configurable in docker-compose.yml)
- Backend: 39011 (configurable in docker-compose.yml)

### Data Persistence

The H2 database data is persisted in the `./data` directory on the host machine.

## Troubleshooting

### Check Container Status
```bash
docker-compose ps
```

### View Individual Service Logs
```bash
# Backend logs
docker-compose logs backend

# Frontend logs
docker-compose logs frontend
```

### Rebuild Containers
```bash
docker-compose down
docker-compose up --build
```

### Clean Up
```bash
# Remove containers and networks
docker-compose down

# Remove containers, networks, and volumes
docker-compose down -v

# Remove images as well
docker-compose down --rmi all
```

## Development

For development purposes, you can:

1. **Override environment variables**:
   Create a `.env` file in the project root with your custom values.

2. **Mount source code** (for development):
   Modify docker-compose.yml to add volume mounts for live reloading.

3. **Use different profiles**:
   Set `SPRING_PROFILES_ACTIVE=dev` for development configuration.

## Production Considerations

For production deployment:

1. Use environment-specific configuration files
2. Set up proper logging and monitoring
3. Configure SSL/TLS termination
4. Use external databases instead of H2
5. Set up proper backup strategies
6. Configure resource limits in docker-compose.yml
