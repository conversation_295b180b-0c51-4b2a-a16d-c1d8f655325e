# 通用网页管理系统

这是一个基于Spring Boot、MyBatis Plus和动态数据源的通用网页管理系统，可以管理外部链接和数据库查询页面。

## 功能特点

1. **统一门户**：在一个页面中管理所有网页链接和数据库查询页面
2. **外部链接管理**：可以添加、编辑、删除外部网页链接
3. **数据库查询页面**：可以配置SQL查询，并提供参数输入界面
4. **动态数据源**：支持多个数据源，包括Oracle数据库
5. **响应式API**：提供RESTful API接口供前端调用

## 技术栈

- Spring Boot 3.5.4
- MyBatis Plus 3.5.7
- Dynamic DataSource 3.5.1
- Oracle JDBC Driver
- H2 Database (用于演示)
- Lombok

## 系统架构

```
┌─────────────────┐
│   前端浏览器     │
└─────────────────┘
         │
┌─────────────────┐
│  Spring Boot    │
│  Controller     │
└─────────────────┘
         │
┌─────────────────┐
│   Service层      │
└─────────────────┘
         │
┌─────────────────┐
│   MyBatis Plus  │
└─────────────────┘
         │
┌──────────────────────────────┐
│      动态数据源管理器          │
├────────────┬─────────────────┤
│  Master    │    Oracle       │
│  (H2)      │    数据源        │
└────────────┴─────────────────┘
```

## 数据库设计

### web_page 表

| 字段名 | 类型 | 描述 |
|-------|------|-----|
| id | BIGINT | 主键ID |
| title | VARCHAR(255) | 页面标题 |
| type | VARCHAR(50) | 页面类型 (link/query) |
| url | VARCHAR(1000) | 外部链接URL |
| query_sql | TEXT | 查询SQL |
| query_params | TEXT | 查询参数定义 |
| description | VARCHAR(1000) | 描述信息 |
| sort | INT | 排序字段 |
| enabled | INT | 是否启用 |

## API接口

### 获取所有启用的网页
```
GET /api/webpages
```

### 根据类型获取网页
```
GET /api/webpages/type/{type}
```

### 获取单个网页详情
```
GET /api/webpages/{id}
```

### 执行数据库查询
```
POST /api/webpages/{id}/query
```

### 创建新的网页
```
POST /api/webpages
```

### 更新网页
```
PUT /api/webpages/{id}
```

### 删除网页
```
DELETE /api/webpages/{id}
```

## 配置说明

### 数据源配置

在 `application.properties` 中配置数据源：

```properties
# 主数据源配置 (默认数据源，用于存储网页配置)
spring.datasource.dynamic.primary=master
spring.datasource.dynamic.strict=false

# 默认数据源 (H2内存数据库，用于演示)
spring.datasource.dynamic.datasource.master.url=jdbc:h2:mem:testdb
spring.datasource.dynamic.datasource.master.driver-class-name=org.h2.Driver
spring.datasource.dynamic.datasource.master.username=sa
spring.datasource.dynamic.datasource.master.password=

# Oracle数据源配置示例 (实际使用时替换为真实的Oracle连接信息)
spring.datasource.dynamic.datasource.oracle.url=*************************************
spring.datasource.dynamic.datasource.oracle.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.dynamic.datasource.oracle.username=your_username
spring.datasource.dynamic.datasource.oracle.password=your_password
```

### 查询参数配置

查询参数使用JSON格式存储，示例：
```json
[
  {
    "name": "name",
    "label": "用户名",
    "type": "text"
  },
  {
    "name": "dept",
    "label": "部门",
    "type": "select",
    "options": ["IT", "HR", "Finance"]
  }
]
```

## 使用方法

1. 启动应用：
   ```bash
   ./gradlew bootRun
   ```

2. 访问H2控制台（仅限测试）：
   浏览器访问 `http://localhost:8080/h2-console`

3. 调用API接口管理网页：
   - 获取所有网页：`GET http://localhost:8080/api/webpages`
   - 执行查询：`POST http://localhost:8080/api/webpages/3/query`

## 安全提醒

1. 生产环境中请修改默认的数据库连接信息
2. 实际应用中应加强SQL注入防护
3. 建议添加用户认证和授权机制
4. 对敏感配置信息进行加密存储