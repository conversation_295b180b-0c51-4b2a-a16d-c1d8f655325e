# 网页管理器前端

这是一个基于React和Bootstrap的网页管理器前端应用，用于管理外部链接和数据库查询页面。

## 功能特点

1. **统一门户**：在一个页面中展示所有网页链接和数据库查询页面
2. **外部链接管理**：可以添加、编辑、删除外部网页链接
3. **数据库查询页面**：可以配置SQL查询，并提供参数输入界面
4. **响应式设计**：适配不同屏幕尺寸的设备

## 技术栈

- React 18
- Bootstrap 5
- React Bootstrap
- React Router
- Axios

## 项目结构

```
frontend/
├── public/                 # 静态资源
├── src/                    # 源代码
│   ├── components/         # 可复用组件
│   ├── pages/              # 页面组件
│   ├── services/           # API服务
│   ├── styles/             # 样式文件
│   ├── App.js              # 主应用组件
│   └── index.js            # 入口文件
├── package.json            # 依赖配置
└── README.md               # 项目说明
```

## 安装和运行

1. 安装依赖：
   ```bash
   cd frontend
   npm install
   ```

2. 启动开发服务器：
   ```bash
   npm start
   ```

3. 构建生产版本：
   ```bash
   npm run build
   ```

## 功能说明

### 主页
- 展示所有网页列表
- 区分外部链接和查询页面
- 提供访问、编辑、删除操作

### 添加/编辑网页
- 支持添加和编辑网页
- 可选择网页类型（外部链接或查询页面）
- 外部链接需要填写URL
- 查询页面需要填写SQL和参数定义

### 查询页面
- 动态生成查询参数输入表单
- 执行数据库查询并展示结果
- 支持多种参数类型（文本框、下拉选择等）

## API接口

前端通过以下API与后端通信：

- `GET /api/webpages` - 获取所有网页
- `GET /api/webpages/type/{type}` - 根据类型获取网页
- `GET /api/webpages/{id}` - 获取单个网页详情
- `POST /api/webpages` - 创建网页
- `PUT /api/webpages/{id}` - 更新网页
- `DELETE /api/webpages/{id}` - 删除网页
- `POST /api/webpages/{id}/query` - 执行查询

## 配置说明

### 代理配置
在`package.json`中配置了代理，将API请求转发到后端：
```json
"proxy": "http://localhost:8080"
```

## 开发说明

### 添加新功能
1. 在`src/pages/`目录下创建新页面组件
2. 在`src/App.js`中添加路由
3. 如需要，创建对应的服务方法

### 自定义样式
1. 全局样式在`src/styles/index.css`中定义
2. 组件特定样式可以在组件文件中使用styled-components或在CSS文件中定义

### 依赖管理
使用npm管理依赖：
```bash
# 添加新依赖
npm install package-name

# 添加开发依赖
npm install --save-dev package-name

# 更新依赖
npm update
```