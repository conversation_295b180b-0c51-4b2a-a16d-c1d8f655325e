{"name": "webpage-manager-frontend", "version": "1.0.0", "description": "Frontend for webpage management system", "main": "index.js", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "axios": "^1.6.0", "bootstrap": "^5.3.0", "react-bootstrap": "^2.8.0", "react-router-dom": "^6.15.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:39010"}