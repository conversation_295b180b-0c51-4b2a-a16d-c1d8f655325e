import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Button, ListGroup } from 'react-bootstrap';
import { Link, useNavigate } from 'react-router-dom';
import { getAllWebPages, deleteWebPage } from '../services/api';

const HomePage = () => {
  const [webPages, setWebPages] = useState([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    loadWebPages();
  }, []);

  const loadWebPages = async () => {
    try {
      const response = await getAllWebPages();
      if (response.data.success) {
        setWebPages(response.data.data);
      }
    } catch (error) {
      console.error('Error loading web pages:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id) => {
    if (window.confirm('确定要删除这个网页吗？')) {
      try {
        await deleteWebPage(id);
        loadWebPages(); // 重新加载数据
      } catch (error) {
        console.error('Error deleting web page:', error);
      }
    }
  };

  const handleEdit = (id) => {
    navigate(`/webpage/${id}`);
  };

  const handleQuery = (id, type) => {
    if (type === 'generic-query') {
      navigate(`/generic-query/${id}`);
    } else {
      navigate(`/query/${id}`);
    }
  };

  return (
    <Container className="main-content">
      <Row>
        <Col>
          <div className="page-header d-flex justify-content-between align-items-center">
            <h1 className="page-title">网页管理</h1>
            <div>
              <Link to="/grouped" className="me-2">
                <Button variant="info">分组视图</Button>
              </Link>
              <Link to="/webpage/new">
                <Button variant="primary">添加网页</Button>
              </Link>
            </div>
          </div>
        </Col>
      </Row>

      <Row>
        <Col>
          <Card>
            <Card.Header>所有网页</Card.Header>
            <Card.Body>
              {loading ? (
                <p>加载中...</p>
              ) : webPages.length === 0 ? (
                <p>暂无网页数据</p>
              ) : (
                <ListGroup>
                  {webPages.map((page) => (
                    <ListGroup.Item key={page.id} className="d-flex justify-content-between align-items-center">
                      <div>
                        <h5>{page.title}</h5>
                        <p className="mb-1">{page.description}</p>
                        <small className="text-muted">
                          类型: {page.type === 'link' ? '外部链接' : page.type === 'generic-query' ? '通用查询页面' : '查询页面'}
                          {page.type === 'link' && ` | URL: ${page.url}`}
                        </small>
                      </div>
                      <div>
                        {page.type === 'link' ? (
                          <a href={page.url} target="_blank" rel="noopener noreferrer">
                            <Button variant="outline-primary" className="btn-action">访问</Button>
                          </a>
                        ) : (
                          <Button variant="outline-info" className="btn-action" onClick={() => handleQuery(page.id, page.type)}>
                            查询
                          </Button>
                        )}
                        <Button variant="outline-secondary" className="btn-action" onClick={() => handleEdit(page.id)}>
                          编辑
                        </Button>
                        <Button variant="outline-danger" onClick={() => handleDelete(page.id)}>
                          删除
                        </Button>
                      </div>
                    </ListGroup.Item>
                  ))}
                </ListGroup>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default HomePage;