import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, Button, Table, Alert, Badge } from 'react-bootstrap';
import { useParams, useNavigate } from 'react-router-dom';
import { getWebPageById, executeDynamicQuery } from '../services/api';

const GenericQueryPage = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [webPage, setWebPage] = useState(null);
  const [params, setParams] = useState({});
  const [customSql, setCustomSql] = useState('');
  const [queryResult, setQueryResult] = useState(null);
  const [loading, setLoading] = useState(false);
  const [executing, setExecuting] = useState(false);
  const [error, setError] = useState(null);
  const [viewMode, setViewMode] = useState('table'); // 'table' or 'keyvalue'

  useEffect(() => {
    loadWebPage();
  }, [id]);

  const loadWebPage = async () => {
    try {
      setLoading(true);
      const response = await getWebPageById(id);
      if (response.data.success) {
        const page = response.data.data;
        setWebPage(page);
        setCustomSql(page.querySql || '');
        
        // 初始化参数
        if (page.queryParams) {
          try {
            const parsedParams = JSON.parse(page.queryParams);
            const initialParams = {};
            parsedParams.forEach(param => {
              if (param.type === 'date') {
                initialParams[param.name] = '2025-01-01';
              } else {
                initialParams[param.name] = '';
              }
            });
            setParams(initialParams);
          } catch (e) {
            console.error('Error parsing query params:', e);
          }
        }
      }
    } catch (error) {
      console.error('Error loading web page:', error);
      setError('加载页面失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleParamChange = (name, value) => {
    setParams({
      ...params,
      [name]: value
    });
  };

  const handleExecuteQuery = async () => {
    try {
      setExecuting(true);
      setError(null);
      
      const response = await executeDynamicQuery(id, params, customSql);
      if (response.data.success) {
        setQueryResult(response.data.data);
      } else {
        setError('查询失败: ' + response.data.message);
      }
    } catch (error) {
      console.error('Error executing query:', error);
      setError('执行查询时发生错误: ' + error.message);
    } finally {
      setExecuting(false);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleExecuteQuery();
    }
  };

  const renderParameterForm = () => {
    if (!webPage?.queryParams) {
      return null;
    }

    try {
      const parsedParams = JSON.parse(webPage.queryParams);
      return (
        <Form>
          <Row>
            {parsedParams.map((param, index) => (
              <Col md={6} key={index}>
                <Form.Group className="mb-3">
                  <Form.Label>{param.label}</Form.Label>
                  {param.type === 'select' && param.options ? (
                    <Form.Select
                      value={params[param.name] || ''}
                      onChange={(e) => handleParamChange(param.name, e.target.value)}
                    >
                      <option value="">请选择</option>
                      {param.options.map((option, optIndex) => (
                        <option key={optIndex} value={option}>
                          {option}
                        </option>
                      ))}
                    </Form.Select>
                  ) : (
                    <>
                      <Form.Control
                        type={param.type || 'text'}
                        value={params[param.name] || ''}
                        onChange={(e) => handleParamChange(param.name, e.target.value)}
                        onKeyPress={handleKeyPress}
                        placeholder={param.placeholder || ''}
                      />
                      {(param.name === 'jobNos' || param.name === 'jobNo') && (
                        <Form.Text className="text-muted">
                          支持多个JOB_NO，用逗号分隔，例如: 12345,2222,3333
                        </Form.Text>
                      )}
                    </>
                  )}
                </Form.Group>
              </Col>
            ))}
          </Row>
        </Form>
      );
    } catch (e) {
      return <Alert variant="warning">查询参数格式错误</Alert>;
    }
  };

  const renderResultTable = (data) => {
    if (!data || data.length === 0) {
      return <Alert variant="info">查询结果为空</Alert>;
    }

    return (
      <div className="table-responsive">
        <Table striped bordered hover size="sm">
          <thead>
            <tr>
              {Object.keys(data[0]).map((key) => (
                <th key={key} style={{ minWidth: '120px' }}>{key}</th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.map((row, index) => (
              <tr key={index}>
                {Object.values(row).map((value, idx) => (
                  <td key={idx} style={{ wordBreak: 'break-word' }}>
                    {value !== null ? value.toString() : ''}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </Table>
      </div>
    );
  };

  const renderKeyValueView = (data) => {
    if (!data || data.length === 0) {
      return null;
    }

    return data.map((record, recordIndex) => (
      <Card key={recordIndex} className="mb-3">
        <Card.Header>
          <strong>记录 {recordIndex + 1}</strong>
        </Card.Header>
        <Card.Body>
          <Table striped bordered hover size="sm">
            <thead>
              <tr>
                <th style={{ width: '30%' }}>字段名</th>
                <th style={{ width: '70%' }}>值</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(record).map(([key, value]) => (
                <tr key={key}>
                  <td><strong>{key}</strong></td>
                  <td style={{ wordBreak: 'break-word' }}>
                    {value !== null ? value.toString() : ''}
                  </td>
                </tr>
              ))}
            </tbody>
          </Table>
        </Card.Body>
      </Card>
    ));
  };

  if (loading) {
    return (
      <Container className="main-content">
        <Alert variant="info">加载中...</Alert>
      </Container>
    );
  }

  if (!webPage) {
    return (
      <Container className="main-content">
        <Alert variant="danger">未找到网页</Alert>
      </Container>
    );
  }

  return (
    <Container className="main-content">
      <Row>
        <Col>
          <div className="page-header d-flex justify-content-between align-items-center">
            <div>
              <h1 className="page-title">通用查询页面: {webPage.title}</h1>
              <div className="text-muted">
                <small>
                  <strong>数据源:</strong> {webPage.datasource || 'master'}
                  {webPage.datasource === 'ptccpro' && ' (Oracle数据库)'}
                  {webPage.datasource === 'ptfsdev1' && ' (Oracle数据库)'}
                  {webPage.datasource === 'ptfspro' && ' (Oracle数据库)'}
                  {(!webPage.datasource || webPage.datasource === 'master') && ' (H2数据库)'}
                </small>
              </div>
            </div>
            <Button variant="secondary" onClick={() => navigate('/')}>
              返回
            </Button>
          </div>
        </Col>
      </Row>

      {/* 查询参数 */}
      <Row>
        <Col>
          <Card>
            <Card.Header>查询参数</Card.Header>
            <Card.Body>
              {renderParameterForm()}
              <Button 
                variant="primary" 
                onClick={handleExecuteQuery} 
                disabled={executing}
                className="me-2"
              >
                {executing ? '查询中...' : '执行查询'}
              </Button>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* SQL编辑器 */}
      <Row>
        <Col>
          <Card className="mt-3">
            <Card.Header>SQL查询语句</Card.Header>
            <Card.Body>
              <Form.Control
                as="textarea"
                rows={6}
                value={customSql}
                onChange={(e) => setCustomSql(e.target.value)}
                placeholder="输入SQL查询语句，使用 :参数名 作为参数占位符"
              />
              <Form.Text className="text-muted">
                可以编辑SQL查询。使用 :参数名 作为参数占位符。支持日期参数自动转换。
              </Form.Text>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* 错误信息 */}
      {error && (
        <Row>
          <Col>
            <Alert variant="danger" className="mt-3">
              {error}
            </Alert>
          </Col>
        </Row>
      )}

      {/* 查询结果 */}
      {queryResult && (
        <>
          <Row>
            <Col>
              <Card className="mt-3">
                <Card.Header className="d-flex justify-content-between align-items-center">
                  <span>查询结果</span>
                  <div>
                    <Badge bg="primary" className="me-2">{queryResult.length} 条记录</Badge>
                    <Button
                      variant={viewMode === 'table' ? 'primary' : 'outline-primary'}
                      size="sm"
                      className="me-1"
                      onClick={() => setViewMode('table')}
                    >
                      表格视图
                    </Button>
                    <Button
                      variant={viewMode === 'keyvalue' ? 'primary' : 'outline-primary'}
                      size="sm"
                      onClick={() => setViewMode('keyvalue')}
                    >
                      详细视图
                    </Button>
                  </div>
                </Card.Header>
                <Card.Body>
                  {viewMode === 'table' ? renderResultTable(queryResult) : renderKeyValueView(queryResult)}
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </>
      )}
    </Container>
  );
};

export default GenericQueryPage;
