import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Card, Form, Button } from 'react-bootstrap';
import { useParams, useNavigate } from 'react-router-dom';
import { getWebPageById, createWebPage, updateWebPage } from '../services/api';

const WebPageForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEdit = !!id;

  const [formData, setFormData] = useState({
    title: '',
    type: 'link',
    url: '',
    querySql: '',
    queryParams: '',
    description: '',
    sort: 0,
    enabled: 1,
    groupName: '',
    groupId: '',
    datasource: 'master'
  });

  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (isEdit) {
      loadWebPage();
    }
  }, [id]);

  const loadWebPage = async () => {
    try {
      const response = await getWebPageById(id);
      if (response.data.success) {
        setFormData(response.data.data);
      }
    } catch (error) {
      console.error('Error loading web page:', error);
    }
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? (checked ? 1 : 0) : value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      if (isEdit) {
        await updateWebPage(id, formData);
      } else {
        await createWebPage(formData);
      }
      navigate('/');
    } catch (error) {
      console.error('Error saving web page:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container className="main-content">
      <Row>
        <Col>
          <div className="page-header">
            <h1 className="page-title">{isEdit ? '编辑网页' : '添加网页'}</h1>
          </div>
        </Col>
      </Row>

      <Row>
        <Col md={8}>
          <Card>
            <Card.Header>{isEdit ? '编辑网页' : '添加网页'}</Card.Header>
            <Card.Body>
              <Form onSubmit={handleSubmit}>
                <Form.Group className="mb-3">
                  <Form.Label>标题 *</Form.Label>
                  <Form.Control
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleChange}
                    required
                  />
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>类型 *</Form.Label>
                  <Form.Select
                    name="type"
                    value={formData.type}
                    onChange={handleChange}
                    required
                  >
                    <option value="link">外部链接</option>
                    <option value="query">查询页面</option>
                    <option value="generic-query">通用查询页面</option>
                  </Form.Select>
                </Form.Group>

                {formData.type === 'link' ? (
                  <Form.Group className="mb-3">
                    <Form.Label>URL *</Form.Label>
                    <Form.Control
                      type="text"
                      name="url"
                      value={formData.url}
                      onChange={handleChange}
                      required
                      placeholder="https://example.com"
                    />
                  </Form.Group>
                ) : (formData.type === 'query' || formData.type === 'generic-query') ? (
                  <>
                    <Form.Group className="mb-3">
                      <Form.Label>数据源 *</Form.Label>
                      <Form.Select
                        name="datasource"
                        value={formData.datasource}
                        onChange={handleChange}
                        required
                      >
                        <option value="master">Master (H2数据库)</option>
                        <option value="ptccpro">PTCCPRO (Oracle数据库)</option>
                        <option value="ptfsdev1">PTFSDEV1 (Oracle数据库)</option>
                        <option value="ptfspro">PTFSPRO (Oracle数据库)</option>
                      </Form.Select>
                      <Form.Text className="text-muted">
                        选择执行查询时使用的数据源
                      </Form.Text>
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>查询SQL *</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={4}
                        name="querySql"
                        value={formData.querySql}
                        onChange={handleChange}
                        required
                        placeholder="SELECT * FROM table WHERE column = :param"
                      />
                    </Form.Group>

                    <Form.Group className="mb-3">
                      <Form.Label>查询参数 (JSON格式)</Form.Label>
                      <Form.Control
                        as="textarea"
                        rows={3}
                        name="queryParams"
                        value={formData.queryParams}
                        onChange={handleChange}
                        placeholder='[{"name":"param","label":"参数名","type":"text"}]'
                      />
                      <Form.Text className="text-muted">
                        定义查询参数，支持类型：text, select, date
                      </Form.Text>
                    </Form.Group>
                  </>
                ): null}

                <Form.Group className="mb-3">
                  <Form.Label>描述</Form.Label>
                  <Form.Control
                    as="textarea"
                    rows={2}
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                  />
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>分组 *</Form.Label>
                  <Form.Select
                    name="groupName"
                    value={formData.groupName}
                    onChange={handleChange}
                    required
                  >
                    <option value="">请选择分组</option>
                    <option value="JIRA">JIRA</option>
                    <option value="Confluence">Confluence</option>
                    <option value="Search">Search</option>
                    <option value="Bitbucket">Bitbucket</option>
                    <option value="Web Portal">Web Portal</option>
                  </Form.Select>
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>分组ID *</Form.Label>
                  <Form.Control
                    type="text"
                    name="groupId"
                    value={formData.groupId}
                    onChange={handleChange}
                    required
                    placeholder="例如: JIRA_001, CONF_001, SEARCH_001, BITBUCKET_001, PORTAL_001"
                  />
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Label>排序</Form.Label>
                  <Form.Control
                    type="number"
                    name="sort"
                    value={formData.sort}
                    onChange={handleChange}
                  />
                </Form.Group>

                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    name="enabled"
                    label="启用"
                    checked={formData.enabled === 1}
                    onChange={handleChange}
                  />
                </Form.Group>

                <div className="d-flex justify-content-between">
                  <Button variant="secondary" onClick={() => navigate('/')}>
                    取消
                  </Button>
                  <Button variant="primary" type="submit" disabled={loading}>
                    {loading ? '保存中...' : '保存'}
                  </Button>
                </div>
              </Form>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Container>
  );
};

export default WebPageForm;