-- 插入示例网页数据 (如果不存在)
INSERT INTO web_page (title, type, url, query_sql, query_params, description, sort, enabled, group_name, group_id, datasource)
SELECT 'CityNet2 ESC_TRIP_DETAILS查询', 'generic-query', NULL, 'SELECT * FROM citynet2.ESC_TRIP_DETAILS etd WHERE JOB_NO IN (:jobNos) and CREATED_DT > :createdDate', '[{"name":"jobNos","label":"JOB_NO (多个用逗号分隔)","type":"text","placeholder":"输入JOB_NO，多个用逗号分隔，如: 12345,2222,3333"},{"name":"createdDate","label":"创建日期","type":"date","placeholder":"选择日期"}]', 'CityNet2数据库ESC_TRIP_DETAILS表查询工具', 9, 1, 'CityNet2', 'CITYNET2_001', 'ptccpro'
WHERE NOT EXISTS (SELECT 1 FROM web_page WHERE title = 'CityNet2 ESC_TRIP_DETAILS查询');

-- 插入测试数据到 ESC_TRIP_DETAILS 表
INSERT INTO ESC_TRIP_DETAILS (JOB_NO, CREATED_DT, TRIP_TYPE, ORIGIN, DESTINATION, STATUS, DRIVER_NAME, VEHICLE_NO)
SELECT 'JOB001', '2025-01-15 10:30:00', 'DELIVERY', 'Singapore', 'Johor Bahru', 'COMPLETED', 'John Doe', 'SG1234A'
WHERE NOT EXISTS (SELECT 1 FROM ESC_TRIP_DETAILS WHERE JOB_NO = 'JOB001');

INSERT INTO ESC_TRIP_DETAILS (JOB_NO, CREATED_DT, TRIP_TYPE, ORIGIN, DESTINATION, STATUS, DRIVER_NAME, VEHICLE_NO)
SELECT 'JOB002', '2025-01-16 14:20:00', 'PICKUP', 'Kuala Lumpur', 'Singapore', 'IN_PROGRESS', 'Jane Smith', 'MY5678B'
WHERE NOT EXISTS (SELECT 1 FROM ESC_TRIP_DETAILS WHERE JOB_NO = 'JOB002');

INSERT INTO ESC_TRIP_DETAILS (JOB_NO, CREATED_DT, TRIP_TYPE, ORIGIN, DESTINATION, STATUS, DRIVER_NAME, VEHICLE_NO)
SELECT 'JOB003', '2025-01-17 09:15:00', 'DELIVERY', 'Bangkok', 'Phuket', 'PENDING', 'Mike Johnson', 'TH9012C'
WHERE NOT EXISTS (SELECT 1 FROM ESC_TRIP_DETAILS WHERE JOB_NO = 'JOB003');

-- Update existing CityNet2 entries to use ptccpro datasource
UPDATE web_page SET datasource = 'ptccpro' WHERE title = 'CityNet2 ESC_TRIP_DETAILS查询' AND datasource IS NULL;