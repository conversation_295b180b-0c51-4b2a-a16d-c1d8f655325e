-- 创建网页表
CREATE TABLE IF NOT EXISTS web_page (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    url VARCHAR(1000),
    query_sql TEXT,
    query_params TEXT,
    description VARCHAR(1000),
    sort INT DEFAULT 0,
    enabled INT DEFAULT 1,
    env VARCHAR(10) DEFAULT 'prod',
    group_name VARCHAR(50),
    group_id VARCHAR(50),
    datasource VARCHAR(50) DEFAULT 'master'
);

-- 添加datasource列到现有表 (如果不存在)
ALTER TABLE web_page ADD COLUMN IF NOT EXISTS datasource VARCHAR(50) DEFAULT 'master';

-- 创建测试表模拟 ESC_TRIP_DETAILS
CREATE TABLE IF NOT EXISTS ESC_TRIP_DETAILS (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    JOB_NO VARCHAR(50) NOT NULL,
    CREATED_DT TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    TRIP_TYPE VARCHAR(20),
    ORIGIN VARCHAR(100),
    DESTINATION VARCHAR(100),
    STATUS VARCHAR(20),
    DRIVER_NAME VARCHAR(100),
    VEHICLE_NO VARCHAR(20)
);