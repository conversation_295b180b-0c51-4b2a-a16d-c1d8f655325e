# Production database configuration
# Dynamic datasource configuration
spring.datasource.dynamic.primary=master
spring.datasource.dynamic.strict=true

# Master datasource (default)
spring.datasource.dynamic.datasource.master.url=******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfsdev1.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfsdev1_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
spring.datasource.dynamic.datasource.master.username=DENG_HUI
spring.datasource.dynamic.datasource.master.password=Oracl3@1234##
spring.datasource.dynamic.datasource.master.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.dynamic.datasource.master.hikari.connection-init-sql=ALTER SESSION SET CURRENT_SCHEMA=recsys

# PTCCPRO datasource
spring.datasource.dynamic.datasource.ptccpro.url=******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=***********))(connect_data=(service_name=gbe786bc9113b95_ptccpro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
spring.datasource.dynamic.datasource.ptccpro.username=CDG_DENG_HUI
spring.datasource.dynamic.datasource.ptccpro.password=Cdg0000278_01
spring.datasource.dynamic.datasource.ptccpro.driver-class-name=oracle.jdbc.OracleDriver

# PTFSPro datasource
spring.datasource.dynamic.datasource.ptfspro.url=******************************= (retry_count=20)(retry_delay=3)(address=(protocol=tcps)(port=1521)(host=ptfspro.adb.ap-singapore-1.oraclecloud.com))(connect_data=(service_name=gbe786bc9113b95_ptfspro_tp.adb.oraclecloud.com))(security=(ssl_server_dn_match=no)))
spring.datasource.dynamic.datasource.ptfspro.username=CDG_DENG_HUI
spring.datasource.dynamic.datasource.ptfspro.password=Cdg0000278_02
spring.datasource.dynamic.datasource.ptfspro.driver-class-name=oracle.jdbc.OracleDriver

# MyBatis Plus configuration
mybatis-plus.mapper-locations=classpath:/mapper/**/*.xml
mybatis-plus.type-aliases-package=com.cdgtaxi.entity
mybatis-plus.global-config.db-config.id-type=auto
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.auto-mapping-behavior=full