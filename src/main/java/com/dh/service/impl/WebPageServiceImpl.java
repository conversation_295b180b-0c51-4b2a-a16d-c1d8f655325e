package com.dh.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dh.entity.WebPage;
import com.dh.mapper.WebPageMapper;
import com.dh.service.WebPageService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class WebPageServiceImpl extends ServiceImpl<WebPageMapper, WebPage> implements WebPageService {
    
    @Autowired
    private WebPageMapper webPageMapper;
    
    @Autowired
    private DataSource dataSource;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public List<WebPage> getAllEnabledWebPages() {
        return webPageMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<WebPage>()
                .eq("enabled", 1)
                .orderByAsc("sort")
        );
    }
    
    @Override
    public List<WebPage> getWebPagesByType(String type) {
        return webPageMapper.selectList(
            new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<WebPage>()
                .eq("enabled", 1)
                .eq("type", type)
                .orderByAsc("sort")
        );
    }
    
    @Override
     @DS("ptccpro") // Commented out temporarily due to database connection issues
    public List<Map<String, Object>> executeQuery(Long webPageId, Map<String, Object> params) {
        WebPage webPage = webPageMapper.selectById(webPageId);
        if (webPage == null || !"query".equals(webPage.getType())) {
            throw new RuntimeException("无效的查询页面");
        }
        
        String sql = webPage.getQuerySql();
        if (sql == null || sql.isEmpty()) {
            throw new RuntimeException("查询SQL为空");
        }
        
        // 替换参数占位符
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value instanceof String) {
                sql = sql.replace(":" + key, "'" + value + "'");
            } else {
                sql = sql.replace(":" + key, value.toString());
            }
        }
        
        log.debug("Executing SQL: {}", sql);

        return queryFromPTCCPRO(sql);
    }

    @Override
    public List<Map<String, Object>> executeDynamicQuery(Long webPageId, Map<String, Object> params, String customSql) {
        // First get webpage details using master datasource (current context)
        WebPage webPage = webPageMapper.selectById(webPageId);
        if (webPage == null || (!"generic-query".equals(webPage.getType()) && !"query".equals(webPage.getType()))) {
            throw new RuntimeException("无效的查询页面");
        }

        String sql;
        if (customSql != null && !customSql.trim().isEmpty()) {
            // 使用自定义SQL
            sql = customSql;
        } else {
            // 使用页面配置的SQL
            sql = webPage.getQuerySql();
            if (sql == null || sql.isEmpty()) {
                throw new RuntimeException("查询SQL为空");
            }
        }

        // 首先处理条件性WHERE子句
        sql = processConditionalClauses(sql, params);

        // 解析查询参数配置以获取参数类型
        Map<String, String> paramTypes = parseParameterTypes(webPage.getQueryParams());

        // 替换参数占位符 - 支持更多类型
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value == null) {
                continue;
            }

            String valueStr = value.toString();
            if (valueStr.isEmpty()) {
                continue;
            }

            // 获取参数类型
            String paramType = paramTypes.get(key);

            // 处理JOB_NO参数 - 支持多个值用逗号分隔
            if ("jobNos".equals(key) || "jobNo".equals(key) || "job_no".equals(key)) {
                String jobNoClause = processJobNoParameter(valueStr);
                sql = sql.replace("JOB_NO=:" + key, "JOB_NO IN (" + jobNoClause + ")");
                sql = sql.replace("JOB_NO = :" + key, "JOB_NO IN (" + jobNoClause + ")");
                sql = sql.replace("JOB_NO IN :" + key, "JOB_NO IN (" + jobNoClause + ")");
                sql = sql.replace("JOB_NO IN (:" + key + ")", "JOB_NO IN (" + jobNoClause + ")");
                sql = sql.replace(":" + key, jobNoClause);
                // 支持通用占位符替换 (如 'xxxx' 替换为实际值)
                sql = sql.replace("'xxxx'", jobNoClause);
                sql = sql.replace("xxxx", jobNoClause);
                continue;
            }

            // 处理BOOKING_ID参数 - 支持多个值用逗号分隔
            if ("bookingIDs".equals(key) || "bookingId".equals(key) || "booking_id".equals(key)) {
                String bookingIdClause = processJobNoParameter(valueStr); // 重用相同的逻辑
                sql = sql.replace("BOOKING_ID=:" + key, "BOOKING_ID IN (" + bookingIdClause + ")");
                sql = sql.replace("BOOKING_ID = :" + key, "BOOKING_ID IN (" + bookingIdClause + ")");
                sql = sql.replace("BOOKING_ID IN :" + key, "BOOKING_ID IN (" + bookingIdClause + ")");
                sql = sql.replace("BOOKING_ID IN (:" + key + ")", "BOOKING_ID IN (" + bookingIdClause + ")");
                sql = sql.replace(":" + key, bookingIdClause);
                continue;
            }

            // 根据query_params中定义的类型处理参数
            if ("date".equals(paramType)) {
                // 如果是日期参数，添加时间部分
                if (valueStr.matches("\\d{4}-\\d{2}-\\d{2}")) {
                    valueStr = valueStr + " 00:00:00";
                }
                sql = sql.replace(":" + key, "to_date('" + valueStr + "', 'yyyy-mm-dd hh24:mi:ss')");
            } else if (value instanceof String) {
                sql = sql.replace(":" + key, "'" + valueStr + "'");
            } else {
                sql = sql.replace(":" + key, valueStr);
            }
        }

        log.debug("Executing Dynamic SQL: {}", sql);

        // Use the datasource specified in the webpage configuration
        String targetDataSource = webPage.getDatasource();
        if (targetDataSource == null || targetDataSource.trim().isEmpty()) {
            targetDataSource = "master"; // Default to master if not specified
        }

        log.info("Using datasource: {} for webpage: {}", targetDataSource, webPage.getTitle());

        // Manually switch to the specified datasource and execute query
        String originalDataSource = DynamicDataSourceContextHolder.peek();
        try {
            DynamicDataSourceContextHolder.push(targetDataSource);
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
            return jdbcTemplate.queryForList(sql);
        } catch (Exception e) {
            log.error("执行{}查询失败", targetDataSource, e);
            throw new RuntimeException("执行" + targetDataSource + "查询失败: " + e.getMessage());
        } finally {
            // Restore original datasource
            DynamicDataSourceContextHolder.clear();
            if (originalDataSource != null) {
                DynamicDataSourceContextHolder.push(originalDataSource);
            }
        }
    }

    @DS("ptccpro")
    private List<Map<String, Object>> executeQueryOnPtccpro(String sql) {
        try {
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
            return jdbcTemplate.queryForList(sql);
        } catch (Exception e) {
            log.error("执行ptccpro查询失败", e);
            throw new RuntimeException("执行ptccpro查询失败: " + e.getMessage());
        }
    }



    /**
     * 使用指定数据源执行查询 (编程方式切换数据源)
     * @param webPageId 网页ID
     * @param params 查询参数
     * @param customSql 自定义SQL
     * @param datasourceName 数据源名称 (ptccpro, ptfsdev1, ptfspro, master)
     * @return 查询结果
     */
    public List<Map<String, Object>> executeDynamicQueryWithDatasource(Long webPageId, Map<String, Object> params, String customSql, String datasourceName) {
        // 手动切换数据源
        DynamicDataSourceContextHolder.push(datasourceName);

        try {
            WebPage webPage = webPageMapper.selectById(webPageId);
            if (webPage == null || !"query".equals(webPage.getType())) {
                throw new RuntimeException("无效的查询页面");
            }

            String sql;
            if (customSql != null && !customSql.trim().isEmpty()) {
                sql = customSql;
            } else {
                sql = webPage.getQuerySql();
                if (sql == null || sql.isEmpty()) {
                    throw new RuntimeException("查询SQL为空");
                }
            }

            // 参数替换逻辑
            for (Map.Entry<String, Object> entry : params.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                if (value == null) {
                    continue;
                }

                String valueStr = value.toString();
                if (valueStr.isEmpty()) {
                    continue;
                }

                // 处理日期类型参数
                if (key.toLowerCase().contains("date") || key.toLowerCase().contains("time")) {
                    if (valueStr.matches("\\d{4}-\\d{2}-\\d{2}")) {
                        valueStr = valueStr + " 00:00:00";
                    }
                    sql = sql.replace(":" + key, "to_date('" + valueStr + "', 'yyyy-mm-dd hh24:mi:ss')");
                } else if (value instanceof String) {
                    sql = sql.replace(":" + key, "'" + valueStr + "'");
                } else {
                    sql = sql.replace(":" + key, valueStr);
                }

                // 支持通用占位符替换
                if ("jobNo".equals(key) || "job_no".equals(key)) {
                    sql = sql.replace("'xxxx'", "'" + valueStr + "'");
                    sql = sql.replace("xxxx", "'" + valueStr + "'");
                }
            }

            return queryFromPTCCPRO(sql);

        } catch (Exception e) {
            log.error("执行动态查询失败 on datasource [{}]", datasourceName, e);
            throw new RuntimeException("执行动态查询失败: " + e.getMessage());
        } finally {
            // 重要：恢复到默认数据源
            DynamicDataSourceContextHolder.poll();
        }
    }

    @DS("ptccpro")
    private List<Map<String, Object>> queryFromPTCCPRO(String sql ) {
        log.debug("Executing SQL on datasource [{}]: {}",   sql);
        try {
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
            return jdbcTemplate.queryForList(sql);
        } catch (Exception e) {
            log.error("执行动态查询失败", e);
            throw new RuntimeException("执行动态查询失败: " + e.getMessage());
        }
    }

    @Override
    @DS("ptccpro")
    public List<Map<String, Object>> testPtccproConnection() {
        log.info("Testing ptccpro datasource connection");
        try {
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
            // Simple test query to verify connection
            return jdbcTemplate.queryForList("SELECT 'Connection successful' as status, SYSDATE as current_time FROM DUAL");
        } catch (Exception e) {
            log.error("ptccpro数据源连接测试失败", e);
            throw new RuntimeException("ptccpro数据源连接测试失败: " + e.getMessage());
        }
    }

    @Override
    @DS("ptccpro")
    public List<Map<String, Object>> executeDirectQuery(String sql) {
        log.info("Executing direct query on ptccpro: {}", sql);
        try {
            JdbcTemplate jdbcTemplate = new JdbcTemplate(dataSource);
            return jdbcTemplate.queryForList(sql);
        } catch (Exception e) {
            log.error("执行直接查询失败", e);
            throw new RuntimeException("执行直接查询失败: " + e.getMessage());
        }
    }

    @Override
    public void updateCityNet2Datasource() {
        log.info("Updating CityNet2 datasource to ptccpro");
        try {
            // Update the CityNet2 entry to use ptccpro datasource
            WebPage cityNet2Page = webPageMapper.selectById(24L);
            if (cityNet2Page != null) {
                cityNet2Page.setDatasource("ptccpro");
                // Also update the SQL to use the correct Oracle schema and IN clause for JOB_NO
                cityNet2Page.setQuerySql("SELECT * FROM citynet2.ESC_TRIP_DETAILS etd WHERE JOB_NO IN :jobNo and CREATED_DT > :createdDate");
                webPageMapper.updateById(cityNet2Page);
                log.info("Successfully updated CityNet2 page datasource to ptccpro");
            } else {
                throw new RuntimeException("CityNet2页面未找到");
            }
        } catch (Exception e) {
            log.error("更新CityNet2数据源失败", e);
            throw new RuntimeException("更新CityNet2数据源失败: " + e.getMessage());
        }
    }

    /**
     * 处理JOB_NO参数，支持多个值用逗号分隔
     * 例如: "12345,2222,3333" -> "'12345','2222','3333'"
     * @param jobNoInput 输入的JOB_NO字符串
     * @return 格式化的值列表（不包含外层括号）
     */
    private String processJobNoParameter(String jobNoInput) {
        if (jobNoInput == null || jobNoInput.trim().isEmpty()) {
            return "''";
        }

        // 分割逗号分隔的值
        String[] jobNumbers = jobNoInput.split(",");
        StringBuilder valueList = new StringBuilder();

        boolean first = true;
        for (String jobNo : jobNumbers) {
            jobNo = jobNo.trim();
            if (!jobNo.isEmpty()) {
                if (!first) {
                    valueList.append(",");
                }
                // 为每个JOB_NO添加单引号
                valueList.append("'").append(jobNo).append("'");
                first = false;
            }
        }

        log.debug("Converted JOB_NO input '{}' to value list: {}", jobNoInput, valueList.toString());
        return valueList.toString();
    }

    /**
     * 处理条件性WHERE子句，只有当参数不为空时才应用相应的条件
     * @param sql 原始SQL
     * @param params 参数映射
     * @return 处理后的SQL
     */
    private String processConditionalClauses(String sql, Map<String, Object> params) {
        // 检查是否有bookingIDs参数且不为空
        boolean hasBookingIDs = hasNonEmptyParam(params, "bookingIDs", "bookingId", "booking_id");

        // 检查是否有jobNos参数且不为空
        boolean hasJobNos = hasNonEmptyParam(params, "jobNos", "jobNo", "job_no");

        log.debug("Conditional processing: hasBookingIDs={}, hasJobNos={}", hasBookingIDs, hasJobNos);

        // 处理 (eb.BOOKING_ID IN (:bookingIDs) OR ej.JOB_NO IN (:jobNos)) 这样的条件
        if (sql.contains("(:bookingIDs)") && sql.contains("(:jobNos)")) {
            if (hasBookingIDs && hasJobNos) {
                // 两个参数都有值，保持OR条件
                log.debug("Both bookingIDs and jobNos provided, keeping OR condition");
            } else if (hasBookingIDs && !hasJobNos) {
                // 只有bookingIDs有值，移除jobNos条件
                // 使用更精确的模式匹配
                String pattern = "\\(([^)]*BOOKING_ID\\s+IN\\s+\\(:[^)]+\\))\\s*OR\\s+([^)]*JOB_NO\\s+IN\\s+\\(:[^)]+\\))\\)";
                sql = sql.replaceAll(pattern, "$1");
                log.debug("Only bookingIDs provided, removed jobNos condition");
            } else if (!hasBookingIDs && hasJobNos) {
                // 只有jobNos有值，移除bookingIDs条件
                // 使用更精确的模式匹配
                String pattern = "\\(([^)]*BOOKING_ID\\s+IN\\s+\\(:[^)]+\\))\\s*OR\\s+([^)]*JOB_NO\\s+IN\\s+\\(:[^)]+\\))\\)";
                sql = sql.replaceAll(pattern, "$2");
                log.debug("Only jobNos provided, removed bookingIDs condition");
            } else {
                // 两个参数都没有值，移除整个OR条件
                String pattern = "\\s*AND\\s*\\([^)]*BOOKING_ID[^)]*OR[^)]*JOB_NO[^)]*\\)";
                sql = sql.replaceAll(pattern, "");
                // 也处理OR条件在AND之前的情况
                pattern = "\\([^)]*BOOKING_ID[^)]*OR[^)]*JOB_NO[^)]*\\)\\s*AND\\s*";
                sql = sql.replaceAll(pattern, "");
                log.debug("Neither parameter provided, removed entire OR condition");
            }
        }

        return sql;
    }

    /**
     * 检查参数是否存在且不为空
     * @param params 参数映射
     * @param paramNames 要检查的参数名列表
     * @return 如果任一参数存在且不为空则返回true
     */
    private boolean hasNonEmptyParam(Map<String, Object> params, String... paramNames) {
        for (String paramName : paramNames) {
            Object value = params.get(paramName);
            if (value != null && !value.toString().trim().isEmpty()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 解析查询参数配置，提取参数类型映射
     * @param queryParams JSON格式的查询参数配置
     * @return 参数名到类型的映射
     */
    private Map<String, String> parseParameterTypes(String queryParams) {
        Map<String, String> paramTypes = new HashMap<>();

        if (queryParams == null || queryParams.trim().isEmpty()) {
            return paramTypes;
        }

        try {
            // 简单的JSON解析，提取参数类型
            // 格式: [{"name":"param1","type":"text"},{"name":"param2","type":"date"}]
            String[] paramBlocks = queryParams.split("\\},\\{");

            for (String block : paramBlocks) {
                // 清理JSON格式字符
                block = block.replaceAll("[\\[\\]{}]", "");

                String paramName = null;
                String paramType = "text"; // 默认类型

                // 提取name和type
                String[] pairs = block.split(",");
                for (String pair : pairs) {
                    if (pair.contains("\"name\"")) {
                        paramName = extractJsonValue(pair);
                    } else if (pair.contains("\"type\"")) {
                        paramType = extractJsonValue(pair);
                    }
                }

                if (paramName != null) {
                    paramTypes.put(paramName, paramType);
                    log.debug("Parsed parameter: {} -> type: {}", paramName, paramType);
                }
            }
        } catch (Exception e) {
            log.warn("Failed to parse query parameters JSON: {}", queryParams, e);
        }

        return paramTypes;
    }

    /**
     * 从JSON键值对中提取值
     * @param jsonPair 格式如 "name":"value"
     * @return 提取的值
     */
    private String extractJsonValue(String jsonPair) {
        try {
            int colonIndex = jsonPair.indexOf(':');
            if (colonIndex > 0) {
                String value = jsonPair.substring(colonIndex + 1).trim();
                // 移除引号
                return value.replaceAll("\"", "");
            }
        } catch (Exception e) {
            log.warn("Failed to extract JSON value from: {}", jsonPair);
        }
        return null;
    }

    @Override
    public void updateCityNet2MultiJobNo() {
        log.info("Updating CityNet2 to support multiple JOB_NO");
        try {
            // Update the CityNet2 entry to support multiple JOB_NO with new format
            WebPage cityNet2Page = webPageMapper.selectById(24L);
            if (cityNet2Page != null) {
                // Update SQL to use IN clause with brackets and new parameter name
                cityNet2Page.setQuerySql("SELECT * FROM citynet2.ESC_TRIP_DETAILS etd WHERE JOB_NO IN (:jobNos) and CREATED_DT > :createdDate");
                // Update parameters with new parameter name
                cityNet2Page.setQueryParams("[{\"name\":\"jobNos\",\"label\":\"JOB_NO (多个用逗号分隔)\",\"type\":\"text\",\"placeholder\":\"输入JOB_NO，多个用逗号分隔，如: 12345,2222,3333\"},{\"name\":\"createdDate\",\"label\":\"创建日期\",\"type\":\"date\",\"placeholder\":\"选择日期\"}]");
                webPageMapper.updateById(cityNet2Page);
                log.info("Successfully updated CityNet2 page to support multiple JOB_NO with new format");
            } else {
                throw new RuntimeException("CityNet2页面未找到");
            }
        } catch (Exception e) {
            log.error("更新CityNet2多JOB_NO支持失败", e);
            throw new RuntimeException("更新CityNet2多JOB_NO支持失败: " + e.getMessage());
        }
    }

    @Override
    public void updateCityNet2Conditional() {
        log.info("Updating CityNet2 to support conditional queries (bookingIDs or jobNos)");
        try {
            // Update the CityNet2 entry to support conditional queries
            WebPage cityNet2Page = webPageMapper.selectById(24L);
            if (cityNet2Page != null) {
                // Update SQL to support conditional OR clause
                String newSql = "SELECT ej.JOB_NO, eb.* FROM CITYNET2.ESC_BOOKING eb " +
                               "LEFT JOIN citynet2.ESC_JOB ej ON eb.BOOKING_ID = ej.BOOKING_ID " +
                               "WHERE 1=1 AND (eb.BOOKING_ID IN (:bookingIDs) OR ej.JOB_NO IN (:jobNos)) " +
                               "AND eb.BOOKING_DT > :bookingDT";

                cityNet2Page.setQuerySql(newSql);

                // Update parameters to include both bookingIDs and jobNos
                String newParams = "[" +
                    "{\"name\":\"bookingIDs\",\"label\":\"BOOKING_ID (多个用逗号分隔)\",\"type\":\"text\",\"placeholder\":\"输入BOOKING_ID，多个用逗号分隔，如: 123,456,789\"}," +
                    "{\"name\":\"jobNos\",\"label\":\"JOB_NO (多个用逗号分隔)\",\"type\":\"text\",\"placeholder\":\"输入JOB_NO，多个用逗号分隔，如: 12345,2222,3333\"}," +
                    "{\"name\":\"bookingDT\",\"label\":\"预订日期\",\"type\":\"date\",\"placeholder\":\"选择日期\"}" +
                    "]";

                cityNet2Page.setQueryParams(newParams);
                cityNet2Page.setDescription("CityNet2数据库条件性查询工具 - 支持按BOOKING_ID或JOB_NO查询");

                webPageMapper.updateById(cityNet2Page);
                log.info("Successfully updated CityNet2 page to support conditional queries");
            } else {
                throw new RuntimeException("CityNet2页面未找到");
            }
        } catch (Exception e) {
            log.error("更新CityNet2条件性查询失败", e);
            throw new RuntimeException("更新CityNet2条件性查询失败: " + e.getMessage());
        }
    }
}