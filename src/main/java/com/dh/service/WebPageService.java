package com.dh.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dh.entity.WebPage;

import java.util.List;
import java.util.Map;

public interface WebPageService extends IService<WebPage> {
    
    /**
     * 获取所有启用的网页列表
     * @return 网页列表
     */
    List<WebPage> getAllEnabledWebPages();
    
    /**
     * 根据类型获取网页列表
     * @param type 网页类型 (link/query)
     * @return 网页列表
     */
    List<WebPage> getWebPagesByType(String type);
    
    /**
     * 执行数据库查询
     * @param webPageId 网页ID
     * @param params 查询参数
     * @return 查询结果
     */
    List<Map<String, Object>> executeQuery(Long webPageId, Map<String, Object> params);

    /**
     * 执行动态SQL查询 (支持自定义SQL)
     * @param webPageId 网页ID
     * @param params 查询参数
     * @param customSql 自定义SQL (可选)
     * @return 查询结果
     */
    List<Map<String, Object>> executeDynamicQuery(Long webPageId, Map<String, Object> params, String customSql);

    /**
     * 使用指定数据源执行查询
     * @param webPageId 网页ID
     * @param params 查询参数
     * @param customSql 自定义SQL (可选)
     * @param datasourceName 数据源名称 (ptccpro, ptfsdev1, ptfspro, master)
     * @return 查询结果
     */
    List<Map<String, Object>> executeDynamicQueryWithDatasource(Long webPageId, Map<String, Object> params, String customSql, String datasourceName);

    /**
     * 测试ptccpro数据源连接
     * @return 测试结果
     */
    List<Map<String, Object>> testPtccproConnection();

    /**
     * 直接执行SQL查询 (使用ptccpro数据源)
     * @param sql SQL查询语句
     * @return 查询结果
     */
    List<Map<String, Object>> executeDirectQuery(String sql);

    /**
     * 更新CityNet2页面的数据源为ptccpro
     */
    void updateCityNet2Datasource();

    /**
     * 更新CityNet2页面支持多个JOB_NO
     */
    void updateCityNet2MultiJobNo();

    /**
     * 更新CityNet2页面支持条件性查询（bookingIDs或jobNos）
     */
    void updateCityNet2Conditional();
}