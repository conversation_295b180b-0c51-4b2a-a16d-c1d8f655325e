package com.dh.controller;

import com.dh.service.WebPageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/test")
@CrossOrigin(origins = "*")
public class TestController {

    @Autowired
    private WebPageService webPageService;

    /**
     * 测试ptccpro数据源连接
     * @return 测试结果
     */
    @GetMapping("/ptccpro")
    public Map<String, Object> testPtccproConnection() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Map<String, Object>> testResult = webPageService.testPtccproConnection();
            result.put("success", true);
            result.put("data", testResult);
            result.put("message", "ptccpro数据源连接成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "ptccpro数据源连接失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 测试CityNet2查询
     * @return 测试结果
     */
    @PostMapping("/citynet2")
    public Map<String, Object> testCityNet2Query(@RequestBody Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        try {
            String jobNo = (String) params.get("jobNo");
            String sql = "SELECT '" + jobNo + "' as JOB_NO, SYSDATE as CREATED_DT FROM DUAL";
            List<Map<String, Object>> testResult = webPageService.executeDirectQuery(sql);
            result.put("success", true);
            result.put("data", testResult);
            result.put("message", "CityNet2查询成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "CityNet2查询失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 更新CityNet2页面的数据源
     * @return 更新结果
     */
    @PostMapping("/update-citynet2-datasource")
    public Map<String, Object> updateCityNet2Datasource() {
        Map<String, Object> result = new HashMap<>();
        try {
            webPageService.updateCityNet2Datasource();
            result.put("success", true);
            result.put("message", "CityNet2数据源更新成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 更新CityNet2页面支持多个JOB_NO
     * @return 更新结果
     */
    @PostMapping("/update-citynet2-multi-jobno")
    public Map<String, Object> updateCityNet2MultiJobNo() {
        Map<String, Object> result = new HashMap<>();
        try {
            webPageService.updateCityNet2MultiJobNo();
            result.put("success", true);
            result.put("message", "CityNet2多JOB_NO支持更新成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 更新CityNet2页面支持条件性查询（bookingIDs或jobNos）
     * @return 更新结果
     */
    @PostMapping("/update-citynet2-conditional")
    public Map<String, Object> updateCityNet2Conditional() {
        Map<String, Object> result = new HashMap<>();
        try {
            webPageService.updateCityNet2Conditional();
            result.put("success", true);
            result.put("message", "CityNet2条件性查询更新成功");
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新失败: " + e.getMessage());
        }
        return result;
    }
}
