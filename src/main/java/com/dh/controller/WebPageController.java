package com.dh.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dh.entity.WebPage;
import com.dh.service.WebPageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/webpages")
@CrossOrigin
public class WebPageController {
    
    @Autowired
    private WebPageService webPageService;
    
    /**
     * 获取所有启用的网页
     * @return 网页列表
     */
    @GetMapping
    public Map<String, Object> getAllWebPages() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<WebPage> webPages = webPageService.getAllEnabledWebPages();
            result.put("success", true);
            result.put("data", webPages);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取网页列表失败: " + e.getMessage());
        }
        return result;
    }
    
    /**
     * 根据类型获取网页
     * @param type 网页类型
     * @return 网页列表
     */
    @GetMapping("/type/{type}")
    public Map<String, Object> getWebPagesByType(@PathVariable String type) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<WebPage> webPages = webPageService.getWebPagesByType(type);
            result.put("success", true);
            result.put("data", webPages);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取网页列表失败: " + e.getMessage());
        }
        return result;
    }
    
    /**
     * 获取单个网页详情
     * @param id 网页ID
     * @return 网页详情
     */
    @GetMapping("/{id}")
    public Map<String, Object> getWebPageById(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            WebPage webPage = webPageService.getById(id);
            if (webPage != null) {
                result.put("success", true);
                result.put("data", webPage);
            } else {
                result.put("success", false);
                result.put("message", "未找到指定网页");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "获取网页详情失败: " + e.getMessage());
        }
        return result;
    }
    
    /**
     * 执行数据库查询
     * @param id 网页ID
     * @param params 查询参数
     * @return 查询结果
     */
    @PostMapping("/{id}/query")
    public Map<String, Object> executeQuery(@PathVariable Long id, @RequestBody Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<Map<String, Object>> queryResult = webPageService.executeQuery(id, params);
            result.put("success", true);
            result.put("data", queryResult);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "执行查询失败: " + e.getMessage());
        }
        return result;
    }
    
    /**
     * 创建新的网页
     * @param webPage 网页对象
     * @return 创建结果
     */
    @PostMapping
    public Map<String, Object> createWebPage(@RequestBody WebPage webPage) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = webPageService.save(webPage);
            if (success) {
                result.put("success", true);
                result.put("message", "创建成功");
                result.put("data", webPage);
            } else {
                result.put("success", false);
                result.put("message", "创建失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "创建网页失败: " + e.getMessage());
        }
        return result;
    }
    
    /**
     * 更新网页
     * @param id 网页ID
     * @param webPage 网页对象
     * @return 更新结果
     */
    @PutMapping("/{id}")
    public Map<String, Object> updateWebPage(@PathVariable Long id, @RequestBody WebPage webPage) {
        Map<String, Object> result = new HashMap<>();
        try {
            webPage.setId(id);
            boolean success = webPageService.updateById(webPage);
            if (success) {
                result.put("success", true);
                result.put("message", "更新成功");
            } else {
                result.put("success", false);
                result.put("message", "更新失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新网页失败: " + e.getMessage());
        }
        return result;
    }
    
    /**
     * 删除网页
     * @param id 网页ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public Map<String, Object> deleteWebPage(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        try {
            boolean success = webPageService.removeById(id);
            if (success) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除网页失败: " + e.getMessage());
        }
        return result;
    }

    /**
     * 执行动态数据库查询 (支持自定义SQL)
     * @param id 网页ID
     * @param requestBody 请求体包含查询参数和可选的自定义SQL
     * @return 查询结果
     */
    @PostMapping("/{id}/dynamic-query")
    public Map<String, Object> executeDynamicQuery(@PathVariable Long id, @RequestBody Map<String, Object> requestBody) {
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Object> params = (Map<String, Object>) requestBody.get("params");
            String customSql = (String) requestBody.get("customSql");

            if (params == null) {
                params = new HashMap<>();
            }

            List<Map<String, Object>> queryResult = webPageService.executeDynamicQuery(id, params, customSql);
            result.put("success", true);
            result.put("data", queryResult);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "执行动态查询失败: " + e.getMessage());
        }
        return result;
    }
}